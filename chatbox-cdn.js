/**
 * Working Chatbox CDN - Auto-initializing chat widget
 * Usage: <script src="working-chatbox.js"></script>
 */

(function() {
    'use strict';

    console.log('🚀 Loading chatbox...');

    // Configuration
    const CONFIG = {
        welcomeMessage: 'Hello! How can I help you today?',
        placeholder: 'Type your message...',
        title: 'Chat Support'
    };

    // Complete CSS embedded in JS
    const CSS = `
        .wb-chatbox-trigger {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.3s ease !important;
            color: white !important;
            font-size: 24px !important;
            outline: none !important;
            font-family: Arial, sans-serif !important;
        }

        .wb-chatbox-trigger:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2) !important;
        }

        .wb-chatbox-trigger.open {
            background: #ff4757 !important;
        }

        .wb-chatbox-container {
            position: fixed !important;
            bottom: 90px !important;
            right: 20px !important;
            width: 350px !important;
            height: 500px !important;
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
            z-index: 999998 !important;
            display: none !important;
            flex-direction: column !important;
            overflow: hidden !important;
            font-family: Arial, sans-serif !important;
        }

        .wb-chatbox-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 16px 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .wb-chatbox-title {
            font-weight: 600 !important;
            font-size: 16px !important;
            color: white !important;
        }

        .wb-chatbox-close {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 20px !important;
            cursor: pointer !important;
            padding: 4px !important;
            border-radius: 4px !important;
            width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
        }

        .chatbox-close:hover {
            background: rgba(255,255,255,0.2) !important;
        }

        .chatbox-messages {
            flex: 1 !important;
            padding: 20px !important;
            overflow-y: auto !important;
            background: #f8f9fa !important;
        }

        .chatbox-message {
            margin-bottom: 12px !important;
            display: flex !important;
        }

        .chatbox-message.user {
            justify-content: flex-end !important;
        }

        .chatbox-message-content {
            max-width: 80% !important;
            padding: 10px 14px !important;
            border-radius: 18px !important;
            font-size: 14px !important;
            line-height: 1.4 !important;
            word-wrap: break-word !important;
        }

        .chatbox-message.bot .chatbox-message-content {
            background: white !important;
            color: #333 !important;
            border: 1px solid #e1e8ed !important;
        }

        .chatbox-message.user .chatbox-message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .chatbox-input-area {
            padding: 20px !important;
            border-top: 1px solid #e1e8ed !important;
            background: white !important;
        }

        .chatbox-input-container {
            display: flex !important;
            gap: 10px !important;
            align-items: center !important;
        }

        .chatbox-input {
            flex: 1 !important;
            padding: 12px 16px !important;
            border: 1px solid #e1e8ed !important;
            border-radius: 24px !important;
            outline: none !important;
            font-size: 14px !important;
            font-family: Arial, sans-serif !important;
        }

        .wb-chatbox-input:focus {
            border-color: #667eea !important;
        }

        .wb-chatbox-send {
            width: 40px !important;
            height: 40px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            color: white !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 16px !important;
            outline: none !important;
        }

        .wb-chatbox-send:hover {
            transform: scale(1.05) !important;
        }

        @media (max-width: 480px) {
            .wb-chatbox-container {
                width: calc(100vw - 40px) !important;
                height: calc(100vh - 40px) !important;
                bottom: 20px !important;
                right: 20px !important;
                left: 20px !important;
                top: 20px !important;
            }
        }
    `;

    // Global variables
    let isOpen = false;
    let messages = [];

    // Inject CSS
    function injectCSS() {
        console.log('💄 Injecting CSS');
        const style = document.createElement('style');
        style.textContent = CSS;
        document.head.appendChild(style);
    }

    // Create chatbox elements
    function createChatbox() {
        console.log('🏗️ Creating chatbox elements');

        // Create trigger button
        const trigger = document.createElement('button');
        trigger.className = 'wb-chatbox-trigger';
        trigger.innerHTML = '💬';
        trigger.onclick = toggleChatbox;

        // Create container
        const container = document.createElement('div');
        container.className = 'wb-chatbox-container';
        container.id = 'wb-chatbox-container';
        container.innerHTML = `
            <div class="wb-chatbox-header">
                <div class="wb-chatbox-title">${CONFIG.title}</div>
                <button class="wb-chatbox-close" onclick="closeChatbox()">×</button>
            </div>
            <div class="wb-chatbox-messages" id="wb-chatbox-messages">
                <div class="wb-chatbox-message bot">
                    <div class="wb-chatbox-message-content">${CONFIG.welcomeMessage}</div>
                </div>
            </div>
            <div class="wb-chatbox-input-area">
                <div class="wb-chatbox-input-container">
                    <input type="text" class="wb-chatbox-input" id="wb-chatbox-input" placeholder="${CONFIG.placeholder}">
                    <button class="wb-chatbox-send" onclick="sendMessage()">➤</button>
                </div>
            </div>
        `;

        // Append to body
        document.body.appendChild(trigger);
        document.body.appendChild(container);

        // Add input event listener
        const input = document.getElementById('wb-chatbox-input');
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        console.log('✅ Chatbox created successfully');
    }

    // Toggle chatbox
    function toggleChatbox() {
        console.log('🔄 Toggling chatbox');
        const container = document.getElementById('wb-chatbox-container');
        const trigger = document.querySelector('.wb-chatbox-trigger');
        
        if (container && trigger) {
            isOpen = !isOpen;
            container.style.display = isOpen ? 'flex' : 'none';
            trigger.classList.toggle('open', isOpen);
            trigger.innerHTML = isOpen ? '×' : '💬';
            
            if (isOpen) {
                setTimeout(() => {
                    const input = document.getElementById('wb-chatbox-input');
                    if (input) input.focus();
                }, 100);
            }
        }
    }

    // Close chatbox
    function closeChatbox() {
        console.log('❌ Closing chatbox');
        const container = document.getElementById('wb-chatbox-container');
        const trigger = document.querySelector('.wb-chatbox-trigger');
        
        if (container && trigger) {
            isOpen = false;
            container.style.display = 'none';
            trigger.classList.remove('open');
            trigger.innerHTML = '💬';
        }
    }

    // Send message
    function sendMessage() {
        const input = document.getElementById('wb-chatbox-input');
        const messagesContainer = document.getElementById('wb-chatbox-messages');
        
        if (!input || !messagesContainer) return;
        
        const text = input.value.trim();
        if (!text) return;

        // Add user message
        addMessage(text, 'user');
        input.value = '';

        // Generate bot response
        setTimeout(() => {
            const response = generateResponse(text);
            addMessage(response, 'bot');
        }, 500);
    }

    // Add message to chat
    function addMessage(text, sender) {
        const messagesContainer = document.getElementById('wb-chatbox-messages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `wb-chatbox-message ${sender}`;
        messageDiv.innerHTML = `<div class="wb-chatbox-message-content">${text}</div>`;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        messages.push({ text, sender, timestamp: Date.now() });
    }

    // Generate bot response
    function generateResponse(userMessage) {
        const responses = {
            hello: "Hello! How can I assist you today? 😊",
            hi: "Hi there! What can I help you with?",
            help: "I'm here to help! You can ask me questions or just chat.",
            thanks: "You're welcome! Is there anything else I can help you with?",
            bye: "Goodbye! Have a great day! 👋",
            default: "That's interesting! Tell me more about that."
        };

        const lower = userMessage.toLowerCase();
        for (const [key, value] of Object.entries(responses)) {
            if (key !== 'default' && lower.includes(key)) {
                return value;
            }
        }
        return responses.default;
    }

    // Make functions global
    window.toggleChatbox = toggleChatbox;
    window.closeChatbox = closeChatbox;
    window.sendMessage = sendMessage;

    // Initialize
    function init() {
        console.log('🎯 Initializing chatbox');
        
        injectCSS();
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createChatbox);
        } else {
            createChatbox();
        }
    }

    // Start initialization
    init();

})();
