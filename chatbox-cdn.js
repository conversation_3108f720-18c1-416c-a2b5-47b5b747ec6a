/**
 * Simple Chatbox CDN - Auto-initializing chat widget
 * Usage: <script src="chatbox-cdn.js"></script>
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        position: 'bottom-right', // bottom-right, bottom-left
        theme: 'blue', // blue, green, purple, dark
        autoOpen: false,
        welcomeMessage: 'Hello! How can I help you today?',
        placeholder: 'Type your message...',
        title: 'Chat Support'
    };

    // CSS Styles
    const CSS = `
        .chatbox-widget * {
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .chatbox-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: white;
            font-size: 24px;
        }

        .chatbox-trigger:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .chatbox-trigger.open {
            background: #ff4757;
        }

        .chatbox-container {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            z-index: 999998;
            display: none;
            flex-direction: column;
            overflow: hidden;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chatbox-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbox-title {
            font-weight: 600;
            font-size: 16px;
        }

        .chatbox-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .chatbox-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .chatbox-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .chatbox-message {
            margin-bottom: 12px;
            display: flex;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chatbox-message.user {
            justify-content: flex-end;
        }

        .chatbox-message-content {
            max-width: 80%;
            padding: 10px 14px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .chatbox-message.bot .chatbox-message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
        }

        .chatbox-message.user .chatbox-message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .chatbox-input-area {
            padding: 20px;
            border-top: 1px solid #e1e8ed;
            background: white;
        }

        .chatbox-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chatbox-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e1e8ed;
            border-radius: 24px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .chatbox-input:focus {
            border-color: #667eea;
        }

        .chatbox-send {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .chatbox-send:hover {
            transform: scale(1.05);
        }

        .chatbox-send:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 480px) {
            .chatbox-container {
                width: calc(100vw - 40px);
                height: calc(100vh - 40px);
                bottom: 20px;
                right: 20px;
                left: 20px;
                top: 20px;
            }
            
            .chatbox-trigger {
                bottom: 20px;
                right: 20px;
            }
        }
    `;

    class SimpleChatbox {
        constructor(config = {}) {
            this.config = { ...CONFIG, ...config };
            this.isOpen = false;
            this.messages = [];
            this.init();
        }

        init() {
            this.injectStyles();
            this.createElements();
            this.bindEvents();
            this.addWelcomeMessage();
        }

        injectStyles() {
            const style = document.createElement('style');
            style.textContent = CSS;
            document.head.appendChild(style);
        }

        createElements() {
            // Create trigger button
            this.trigger = document.createElement('button');
            this.trigger.className = 'chatbox-trigger';
            this.trigger.innerHTML = '💬';
            this.trigger.setAttribute('aria-label', 'Open chat');

            // Create container
            this.container = document.createElement('div');
            this.container.className = 'chatbox-container';
            this.container.innerHTML = `
                <div class="chatbox-header">
                    <div class="chatbox-title">${this.config.title}</div>
                    <button class="chatbox-close" aria-label="Close chat">×</button>
                </div>
                <div class="chatbox-messages"></div>
                <div class="chatbox-input-area">
                    <div class="chatbox-input-container">
                        <input type="text" class="chatbox-input" placeholder="${this.config.placeholder}">
                        <button class="chatbox-send" aria-label="Send message">➤</button>
                    </div>
                </div>
            `;

            // Append to body
            document.body.appendChild(this.trigger);
            document.body.appendChild(this.container);

            // Get references
            this.messagesEl = this.container.querySelector('.chatbox-messages');
            this.inputEl = this.container.querySelector('.chatbox-input');
            this.sendBtn = this.container.querySelector('.chatbox-send');
            this.closeBtn = this.container.querySelector('.chatbox-close');
        }

        bindEvents() {
            this.trigger.addEventListener('click', () => this.toggle());
            this.closeBtn.addEventListener('click', () => this.close());
            this.sendBtn.addEventListener('click', () => this.sendMessage());
            this.inputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.sendMessage();
            });

            // Close on escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) this.close();
            });
        }

        toggle() {
            this.isOpen ? this.close() : this.open();
        }

        open() {
            this.isOpen = true;
            this.container.style.display = 'flex';
            this.trigger.classList.add('open');
            this.trigger.innerHTML = '×';
            setTimeout(() => this.inputEl.focus(), 300);
        }

        close() {
            this.isOpen = false;
            this.container.style.display = 'none';
            this.trigger.classList.remove('open');
            this.trigger.innerHTML = '💬';
        }

        addMessage(text, sender = 'bot') {
            const messageEl = document.createElement('div');
            messageEl.className = `chatbox-message ${sender}`;
            messageEl.innerHTML = `<div class="chatbox-message-content">${text}</div>`;
            
            this.messagesEl.appendChild(messageEl);
            this.messagesEl.scrollTop = this.messagesEl.scrollHeight;
            
            this.messages.push({ text, sender, timestamp: Date.now() });
        }

        addWelcomeMessage() {
            if (this.config.welcomeMessage) {
                this.addMessage(this.config.welcomeMessage, 'bot');
            }
        }

        sendMessage() {
            const text = this.inputEl.value.trim();
            if (!text) return;

            this.addMessage(text, 'user');
            this.inputEl.value = '';

            // Simulate bot response
            setTimeout(() => {
                this.generateResponse(text);
            }, 500);
        }

        generateResponse(userMessage) {
            const responses = {
                hello: "Hello! How can I assist you today?",
                hi: "Hi there! What can I help you with?",
                help: "I'm here to help! You can ask me questions or just chat.",
                thanks: "You're welcome! Is there anything else I can help you with?",
                bye: "Goodbye! Have a great day!",
                default: "That's interesting! Tell me more about that."
            };

            const lower = userMessage.toLowerCase();
            let response = responses.default;

            for (const [key, value] of Object.entries(responses)) {
                if (key !== 'default' && lower.includes(key)) {
                    response = value;
                    break;
                }
            }

            this.addMessage(response, 'bot');
        }
    }

    // Auto-initialize when DOM is ready
    function initChatbox() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.SimpleChatbox = new SimpleChatbox();
            });
        } else {
            window.SimpleChatbox = new SimpleChatbox();
        }
    }

    initChatbox();

})();
