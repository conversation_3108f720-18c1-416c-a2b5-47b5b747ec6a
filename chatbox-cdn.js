/**
 * Simple Chatbox CDN - Auto-initializing chat widget
 * Usage: <script src="chatbox-cdn.js"></script>
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        position: 'bottom-right', // bottom-right, bottom-left
        theme: 'blue', // blue, green, purple, dark
        autoOpen: false,
        welcomeMessage: 'Hello! How can I help you today?',
        placeholder: 'Type your message...',
        title: 'Chat Support'
    };

    // Complete CSS Styles - Everything embedded in JS
    const CSS = `
        /* Reset and base styles for chatbox */
        .chatbox-widget, .chatbox-widget * {
            box-sizing: border-box !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Floating chat trigger button */
        .chatbox-trigger {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.3s ease !important;
            color: white !important;
            font-size: 24px !important;
            outline: none !important;
            user-select: none !important;
        }

        .chatbox-trigger:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2) !important;
        }

        .chatbox-trigger:active {
            transform: scale(0.95) !important;
        }

        .chatbox-trigger.open {
            background: #ff4757 !important;
            transform: rotate(45deg) !important;
        }

        .chatbox-trigger.open:hover {
            transform: rotate(45deg) scale(1.1) !important;
        }

        /* Main chatbox container */
        .chatbox-container {
            position: fixed !important;
            bottom: 90px !important;
            right: 20px !important;
            width: 350px !important;
            height: 500px !important;
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
            z-index: 999998 !important;
            display: none !important;
            flex-direction: column !important;
            overflow: hidden !important;
            animation: slideUp 0.3s ease !important;
            font-size: 14px !important;
        }

        /* Slide up animation */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Chatbox header */
        .chatbox-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 16px 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            border-radius: 12px 12px 0 0 !important;
        }

        .chatbox-title {
            font-weight: 600 !important;
            font-size: 16px !important;
            color: white !important;
        }

        .chatbox-close {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 20px !important;
            cursor: pointer !important;
            padding: 4px !important;
            border-radius: 4px !important;
            transition: background 0.2s !important;
            width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
        }

        .chatbox-close:hover {
            background: rgba(255,255,255,0.2) !important;
        }

        /* Messages area */
        .chatbox-messages {
            flex: 1 !important;
            padding: 20px !important;
            overflow-y: auto !important;
            background: #f8f9fa !important;
            max-height: 350px !important;
        }

        .chatbox-messages::-webkit-scrollbar {
            width: 6px !important;
        }

        .chatbox-messages::-webkit-scrollbar-track {
            background: transparent !important;
        }

        .chatbox-messages::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2) !important;
            border-radius: 3px !important;
        }

        .chatbox-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0,0,0,0.3) !important;
        }

        /* Individual messages */
        .chatbox-message {
            margin-bottom: 12px !important;
            display: flex !important;
            animation: fadeIn 0.3s ease !important;
            clear: both !important;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chatbox-message.user {
            justify-content: flex-end !important;
        }

        .chatbox-message-content {
            max-width: 80% !important;
            padding: 10px 14px !important;
            border-radius: 18px !important;
            font-size: 14px !important;
            line-height: 1.4 !important;
            word-wrap: break-word !important;
            position: relative !important;
        }

        .chatbox-message.bot .chatbox-message-content {
            background: white !important;
            color: #333 !important;
            border: 1px solid #e1e8ed !important;
            border-radius: 18px 18px 18px 4px !important;
        }

        .chatbox-message.user .chatbox-message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-radius: 18px 18px 4px 18px !important;
        }

        /* Input area */
        .chatbox-input-area {
            padding: 20px !important;
            border-top: 1px solid #e1e8ed !important;
            background: white !important;
            border-radius: 0 0 12px 12px !important;
        }

        .chatbox-input-container {
            display: flex !important;
            gap: 10px !important;
            align-items: center !important;
        }

        .chatbox-input {
            flex: 1 !important;
            padding: 12px 16px !important;
            border: 1px solid #e1e8ed !important;
            border-radius: 24px !important;
            outline: none !important;
            font-size: 14px !important;
            transition: border-color 0.2s !important;
            background: white !important;
            color: #333 !important;
        }

        .chatbox-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }

        .chatbox-input::placeholder {
            color: #999 !important;
        }

        .chatbox-send {
            width: 40px !important;
            height: 40px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            color: white !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: transform 0.2s !important;
            font-size: 16px !important;
            outline: none !important;
        }

        .chatbox-send:hover {
            transform: scale(1.05) !important;
        }

        .chatbox-send:active {
            transform: scale(0.95) !important;
        }

        .chatbox-send:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

        /* Typing indicator */
        .chatbox-typing {
            display: flex !important;
            align-items: center !important;
            padding: 10px 14px !important;
            background: white !important;
            border: 1px solid #e1e8ed !important;
            border-radius: 18px 18px 18px 4px !important;
            margin-bottom: 12px !important;
            max-width: 80% !important;
        }

        .chatbox-typing-dots {
            display: flex !important;
            gap: 4px !important;
        }

        .chatbox-typing-dot {
            width: 6px !important;
            height: 6px !important;
            background: #999 !important;
            border-radius: 50% !important;
            animation: typingDot 1.4s infinite ease-in-out !important;
        }

        .chatbox-typing-dot:nth-child(1) { animation-delay: -0.32s !important; }
        .chatbox-typing-dot:nth-child(2) { animation-delay: -0.16s !important; }

        @keyframes typingDot {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Mobile responsive */
        @media (max-width: 480px) {
            .chatbox-container {
                width: calc(100vw - 40px) !important;
                height: calc(100vh - 40px) !important;
                bottom: 20px !important;
                right: 20px !important;
                left: 20px !important;
                top: 20px !important;
                border-radius: 0 !important;
            }

            .chatbox-trigger {
                bottom: 20px !important;
                right: 20px !important;
            }

            .chatbox-header {
                border-radius: 0 !important;
            }

            .chatbox-input-area {
                border-radius: 0 !important;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .chatbox-messages {
                background: #1a1a1a !important;
            }

            .chatbox-message.bot .chatbox-message-content {
                background: #2d2d2d !important;
                color: #e1e1e1 !important;
                border-color: #404040 !important;
            }

            .chatbox-input-area {
                background: #2d2d2d !important;
                border-top-color: #404040 !important;
            }

            .chatbox-input {
                background: #1a1a1a !important;
                color: #e1e1e1 !important;
                border-color: #404040 !important;
            }

            .chatbox-input::placeholder {
                color: #888 !important;
            }
        }
    `;

    class SimpleChatbox {
        constructor(config = {}) {
            this.config = { ...CONFIG, ...config };
            this.isOpen = false;
            this.messages = [];
            this.init();
        }

        init() {
            this.injectStyles();
            this.createElements();
            this.bindEvents();
            this.addWelcomeMessage();
        }

        injectStyles() {
            const style = document.createElement('style');
            style.textContent = CSS;
            document.head.appendChild(style);
        }

        createElements() {
            // Create trigger button
            this.trigger = document.createElement('button');
            this.trigger.className = 'chatbox-trigger chatbox-widget';
            this.trigger.innerHTML = '💬';
            this.trigger.setAttribute('aria-label', 'Open chat');
            this.trigger.setAttribute('title', 'Open chat');

            // Create container
            this.container = document.createElement('div');
            this.container.className = 'chatbox-container chatbox-widget';
            this.container.innerHTML = `
                <div class="chatbox-header">
                    <div class="chatbox-title">${this.config.title}</div>
                    <button class="chatbox-close" aria-label="Close chat" title="Close chat">×</button>
                </div>
                <div class="chatbox-messages"></div>
                <div class="chatbox-input-area">
                    <div class="chatbox-input-container">
                        <input type="text" class="chatbox-input" placeholder="${this.config.placeholder}" autocomplete="off" spellcheck="false">
                        <button class="chatbox-send" aria-label="Send message" title="Send message">➤</button>
                    </div>
                </div>
            `;

            // Ensure elements are added to body when DOM is ready
            this.appendToBody();

            // Get references
            this.messagesEl = this.container.querySelector('.chatbox-messages');
            this.inputEl = this.container.querySelector('.chatbox-input');
            this.sendBtn = this.container.querySelector('.chatbox-send');
            this.closeBtn = this.container.querySelector('.chatbox-close');
        }

        appendToBody() {
            // Make sure we don't add duplicates
            if (document.querySelector('.chatbox-trigger')) return;

            // Append to body
            document.body.appendChild(this.trigger);
            document.body.appendChild(this.container);
        }

        bindEvents() {
            this.trigger.addEventListener('click', () => this.toggle());
            this.closeBtn.addEventListener('click', () => this.close());
            this.sendBtn.addEventListener('click', () => this.sendMessage());
            this.inputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.sendMessage();
            });

            // Close on escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) this.close();
            });
        }

        toggle() {
            this.isOpen ? this.close() : this.open();
        }

        open() {
            this.isOpen = true;
            this.container.style.display = 'flex';
            this.trigger.classList.add('open');
            this.trigger.innerHTML = '×';
            setTimeout(() => this.inputEl.focus(), 300);
        }

        close() {
            this.isOpen = false;
            this.container.style.display = 'none';
            this.trigger.classList.remove('open');
            this.trigger.innerHTML = '💬';
        }

        addMessage(text, sender = 'bot') {
            const messageEl = document.createElement('div');
            messageEl.className = `chatbox-message ${sender}`;
            messageEl.innerHTML = `<div class="chatbox-message-content">${text}</div>`;
            
            this.messagesEl.appendChild(messageEl);
            this.messagesEl.scrollTop = this.messagesEl.scrollHeight;
            
            this.messages.push({ text, sender, timestamp: Date.now() });
        }

        addWelcomeMessage() {
            if (this.config.welcomeMessage) {
                this.addMessage(this.config.welcomeMessage, 'bot');
            }
        }

        sendMessage() {
            const text = this.inputEl.value.trim();
            if (!text) return;

            // Disable send button temporarily
            this.sendBtn.disabled = true;

            this.addMessage(text, 'user');
            this.inputEl.value = '';

            // Show typing indicator
            this.showTypingIndicator();

            // Simulate bot response with realistic delay
            const delay = Math.random() * 1000 + 800; // 800-1800ms
            setTimeout(() => {
                this.hideTypingIndicator();
                this.generateResponse(text);
                this.sendBtn.disabled = false;
            }, delay);
        }

        showTypingIndicator() {
            if (this.typingIndicator) return;

            this.typingIndicator = document.createElement('div');
            this.typingIndicator.className = 'chatbox-typing';
            this.typingIndicator.innerHTML = `
                <div class="chatbox-typing-dots">
                    <div class="chatbox-typing-dot"></div>
                    <div class="chatbox-typing-dot"></div>
                    <div class="chatbox-typing-dot"></div>
                </div>
            `;

            this.messagesEl.appendChild(this.typingIndicator);
            this.messagesEl.scrollTop = this.messagesEl.scrollHeight;
        }

        hideTypingIndicator() {
            if (this.typingIndicator) {
                this.typingIndicator.remove();
                this.typingIndicator = null;
            }
        }

        generateResponse(userMessage) {
            const responses = {
                hello: "Hello! How can I assist you today? 😊",
                hi: "Hi there! What can I help you with?",
                hey: "Hey! How's it going?",
                help: "I'm here to help! You can ask me questions or just chat. What do you need assistance with?",
                support: "I'm your support assistant! How can I help you today?",
                thanks: "You're very welcome! Is there anything else I can help you with?",
                thank: "You're welcome! Feel free to ask if you need anything else.",
                bye: "Goodbye! Have a wonderful day! 👋",
                goodbye: "Take care! Feel free to come back anytime you need help.",
                how: "That's a great question! I'm here to help you figure it out. Can you tell me more?",
                what: "Let me help you with that! Could you provide a bit more detail?",
                price: "For pricing information, I'd recommend checking our pricing page or speaking with our sales team.",
                contact: "You can reach us through this chat, email, or phone. What works best for you?",
                problem: "I'm sorry to hear you're having an issue. Can you describe what's happening?",
                error: "Let's get that error sorted out! Can you tell me more about what you're experiencing?",
                default: "That's interesting! I'd love to help you with that. Can you tell me more?"
            };

            const lower = userMessage.toLowerCase();
            let response = responses.default;

            // Check for keywords
            for (const [key, value] of Object.entries(responses)) {
                if (key !== 'default' && lower.includes(key)) {
                    response = value;
                    break;
                }
            }

            // Add some personality based on message length
            if (userMessage.length > 100) {
                response = "Wow, thanks for the detailed message! " + response;
            }

            this.addMessage(response, 'bot');
        }
    }

    // Utility functions for external access
    window.ChatboxAPI = {
        // Send a message programmatically
        sendMessage: function(message, sender = 'user') {
            if (window.SimpleChatbox) {
                window.SimpleChatbox.addMessage(message, sender);
            }
        },

        // Open the chatbox
        open: function() {
            if (window.SimpleChatbox) {
                window.SimpleChatbox.open();
            }
        },

        // Close the chatbox
        close: function() {
            if (window.SimpleChatbox) {
                window.SimpleChatbox.close();
            }
        },

        // Clear all messages
        clear: function() {
            if (window.SimpleChatbox && window.SimpleChatbox.messagesEl) {
                window.SimpleChatbox.messagesEl.innerHTML = '';
                window.SimpleChatbox.messages = [];
                window.SimpleChatbox.addWelcomeMessage();
            }
        },

        // Get all messages
        getMessages: function() {
            return window.SimpleChatbox ? window.SimpleChatbox.messages : [];
        },

        // Check if chatbox is open
        isOpen: function() {
            return window.SimpleChatbox ? window.SimpleChatbox.isOpen : false;
        }
    };

    // Auto-initialize when DOM is ready
    function initChatbox() {
        // Prevent multiple initializations
        if (window.SimpleChatbox) return;

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    window.SimpleChatbox = new SimpleChatbox();
                    console.log('🚀 Simple Chatbox initialized successfully!');
                }, 100);
            });
        } else {
            // DOM is already ready
            setTimeout(() => {
                window.SimpleChatbox = new SimpleChatbox();
                console.log('🚀 Simple Chatbox initialized successfully!');
            }, 100);
        }
    }

    // Initialize immediately
    initChatbox();

    // Expose for debugging
    if (typeof window !== 'undefined') {
        window.SimpleChatboxVersion = '1.0.0';
    }

})();
