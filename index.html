<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Modal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }

        .open-chat-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 20px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: background-color 0.3s;
        }

        .open-chat-btn:hover {
            background-color: #0056b3;
        }

        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .chatbox-modal {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 500px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
            z-index: 1001;
        }

        .chat-header {
            background-color: #007bff;
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-weight: bold;
            font-size: 16px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-radius: 50%;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: #f8f9fa;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.bot {
            background-color: #e9ecef;
            color: #333;
        }

        .chat-input-container {
            padding: 15px;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
        }

        .chat-input:focus {
            border-color: #007bff;
        }

        .send-btn {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .send-btn:hover {
            background-color: #0056b3;
        }

        .send-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        @media (max-width: 480px) {
            .chatbox-modal {
                width: calc(100% - 40px);
                height: calc(100% - 40px);
                bottom: 20px;
                right: 20px;
                left: 20px;
                top: 20px;
            }
        }
    </style>
</head>
<body>
    <h1>Simple Chatbox Modal Demo</h1>
    <p>Click the chat button in the bottom right corner to open the chatbox modal.</p>

    <!-- Chat Button -->
    <button class="open-chat-btn" id="openChatBtn">💬 Chat</button>

    <!-- Modal Overlay -->
    <div class="modal-overlay" id="modalOverlay"></div>

    <!-- Chatbox Modal -->
    <div class="chatbox-modal" id="chatboxModal" style="display: none;">
        <div class="chat-header">
            <div class="chat-title">Chat Support</div>
            <button class="close-btn" id="closeChatBtn">&times;</button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                Hello! How can I help you today?
            </div>
        </div>
        <div class="chat-input-container">
            <input type="text" class="chat-input" id="chatInput" placeholder="Type your message...">
            <button class="send-btn" id="sendBtn">➤</button>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>