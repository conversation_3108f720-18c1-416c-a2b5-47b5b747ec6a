/**
 * Simple Chatbox - Debug Version
 */

(function() {
    'use strict';

    console.log('🚀 Chatbox script loaded');

    // Simple CSS
    const CSS = `
        .simple-chatbox-trigger {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: #007bff !important;
            border: none !important;
            border-radius: 50% !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .simple-chatbox-container {
            position: fixed !important;
            bottom: 90px !important;
            right: 20px !important;
            width: 300px !important;
            height: 400px !important;
            background: white !important;
            border: 1px solid #ccc !important;
            border-radius: 8px !important;
            z-index: 999998 !important;
            display: none !important;
            flex-direction: column !important;
        }

        .simple-chatbox-header {
            background: #007bff !important;
            color: white !important;
            padding: 10px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .simple-chatbox-messages {
            flex: 1 !important;
            padding: 10px !important;
            overflow-y: auto !important;
            background: #f8f9fa !important;
        }

        .simple-chatbox-input-area {
            padding: 10px !important;
            border-top: 1px solid #ccc !important;
        }

        .simple-chatbox-input {
            width: 100% !important;
            padding: 8px !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
        }
    `;

    function injectCSS() {
        console.log('💄 Injecting CSS');
        const style = document.createElement('style');
        style.textContent = CSS;
        document.head.appendChild(style);
    }

    function createChatbox() {
        console.log('🏗️ Creating chatbox elements');
        
        // Create trigger button
        const trigger = document.createElement('button');
        trigger.className = 'simple-chatbox-trigger';
        trigger.innerHTML = '💬';
        trigger.onclick = toggleChatbox;

        // Create container
        const container = document.createElement('div');
        container.className = 'simple-chatbox-container';
        container.id = 'simple-chatbox-container';
        container.innerHTML = `
            <div class="simple-chatbox-header">
                <span>Chat</span>
                <button onclick="closeChatbox()" style="background:none;border:none;color:white;cursor:pointer;">×</button>
            </div>
            <div class="simple-chatbox-messages">
                <div>Hello! How can I help you?</div>
            </div>
            <div class="simple-chatbox-input-area">
                <input type="text" class="simple-chatbox-input" placeholder="Type a message...">
            </div>
        `;

        console.log('📎 Appending to body');
        document.body.appendChild(trigger);
        document.body.appendChild(container);
        
        console.log('✅ Chatbox created successfully');
    }

    function toggleChatbox() {
        console.log('🔄 Toggling chatbox');
        const container = document.getElementById('simple-chatbox-container');
        if (container) {
            const isVisible = container.style.display === 'flex';
            container.style.display = isVisible ? 'none' : 'flex';
            console.log('👁️ Chatbox visibility:', !isVisible);
        }
    }

    function closeChatbox() {
        console.log('❌ Closing chatbox');
        const container = document.getElementById('simple-chatbox-container');
        if (container) {
            container.style.display = 'none';
        }
    }

    // Make functions global for onclick handlers
    window.toggleChatbox = toggleChatbox;
    window.closeChatbox = closeChatbox;

    function init() {
        console.log('🎯 Initializing chatbox');
        console.log('📄 Document ready state:', document.readyState);
        
        injectCSS();
        
        if (document.readyState === 'loading') {
            console.log('⏳ Waiting for DOM...');
            document.addEventListener('DOMContentLoaded', () => {
                console.log('✅ DOM loaded, creating chatbox');
                createChatbox();
            });
        } else {
            console.log('✅ DOM already ready, creating chatbox');
            createChatbox();
        }
    }

    // Initialize immediately
    init();

})();
