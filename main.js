// Chatbox Modal JavaScript
class ChatboxModal {
    constructor() {
        this.isOpen = false;
        this.initializeElements();
        this.bindEvents();
        this.messages = [];
    }

    initializeElements() {
        this.openBtn = document.getElementById('openChatBtn');
        this.closeBtn = document.getElementById('closeChatBtn');
        this.modal = document.getElementById('chatboxModal');
        this.overlay = document.getElementById('modalOverlay');
        this.messagesContainer = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendBtn = document.getElementById('sendBtn');
    }

    bindEvents() {
        // Open modal
        this.openBtn.addEventListener('click', () => this.openModal());

        // Close modal
        this.closeBtn.addEventListener('click', () => this.closeModal());
        this.overlay.addEventListener('click', () => this.closeModal());

        // Send message
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // Escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeModal();
            }
        });
    }

    openModal() {
        this.isOpen = true;
        this.overlay.style.display = 'block';
        this.modal.style.display = 'flex';
        this.openBtn.style.display = 'none';

        // Focus on input
        setTimeout(() => {
            this.chatInput.focus();
        }, 100);
    }

    closeModal() {
        this.isOpen = false;
        this.overlay.style.display = 'none';
        this.modal.style.display = 'none';
        this.openBtn.style.display = 'block';
    }

    sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        this.chatInput.value = '';

        // Simulate bot response after a short delay
        setTimeout(() => {
            this.generateBotResponse(message);
        }, 500);
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = text;

        this.messagesContainer.appendChild(messageDiv);

        // Scroll to bottom
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;

        // Store message
        this.messages.push({ text, sender, timestamp: new Date() });
    }

    generateBotResponse(userMessage) {
        // Simple bot responses based on keywords
        const responses = {
            hello: "Hello! How can I assist you today?",
            hi: "Hi there! What can I help you with?",
            help: "I'm here to help! You can ask me questions or just chat.",
            thanks: "You're welcome! Is there anything else I can help you with?",
            thank: "You're welcome! Is there anything else I can help you with?",
            bye: "Goodbye! Have a great day!",
            goodbye: "Goodbye! Feel free to come back anytime!",
            how: "That's a great question! I'm here to help you figure it out.",
            what: "Let me help you with that. Can you provide more details?",
            default: "That's interesting! Tell me more about that."
        };

        const lowerMessage = userMessage.toLowerCase();
        let response = responses.default;

        // Check for keywords
        for (const [keyword, reply] of Object.entries(responses)) {
            if (keyword !== 'default' && lowerMessage.includes(keyword)) {
                response = reply;
                break;
            }
        }

        this.addMessage(response, 'bot');
    }

    // Public methods
    clearChat() {
        this.messagesContainer.innerHTML = '<div class="message bot">Hello! How can I help you today?</div>';
        this.messages = [];
    }

    getMessages() {
        return this.messages;
    }
}

// Initialize the chatbox when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const chatbox = new ChatboxModal();

    // Make chatbox globally accessible for debugging
    window.chatbox = chatbox;
});

// Optional: Add some utility functions
function addCustomMessage(text, sender = 'bot') {
    if (window.chatbox) {
        window.chatbox.addMessage(text, sender);
    }
}

function clearChat() {
    if (window.chatbox) {
        window.chatbox.clearChat();
    }
}