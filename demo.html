<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox CDN Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature h3 {
            margin-top: 0;
            font-size: 1.5rem;
        }
        
        .code-block {
            background: rgba(0,0,0,0.3);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .highlight {
            background: rgba(255,255,255,0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Simple Chatbox CDN</h1>
        <p>A lightweight, auto-initializing chatbox widget that works just like Crisp!</p>
        
        <div class="code-block">
            <div class="highlight">Usage - Just add one line:</div>
            <br>
            &lt;script src="chatbox-cdn.js"&gt;&lt;/script&gt;
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🎯 Auto-Initialize</h3>
                <p>Automatically shows up when the script is loaded, no configuration needed!</p>
            </div>
            
            <div class="feature">
                <h3>📱 Responsive</h3>
                <p>Works perfectly on desktop and mobile devices with adaptive sizing.</p>
            </div>
            
            <div class="feature">
                <h3>🎨 Beautiful UI</h3>
                <p>Modern gradient design with smooth animations and hover effects.</p>
            </div>
            
            <div class="feature">
                <h3>⚡ Lightweight</h3>
                <p>Pure JavaScript, no dependencies. Less than 10KB minified.</p>
            </div>
            
            <div class="feature">
                <h3>🤖 Smart Responses</h3>
                <p>Built-in bot responses based on keywords in user messages.</p>
            </div>
            
            <div class="feature">
                <h3>⌨️ Keyboard Friendly</h3>
                <p>Enter to send, Escape to close, and proper focus management.</p>
            </div>
        </div>
        
        <p style="margin-top: 3rem; font-size: 1rem; opacity: 0.8;">
            Look for the chat button in the bottom-right corner! 💬
        </p>
    </div>

    <!-- This is all you need to add the chatbox! -->
    <script src="chatbox-cdn.js"></script>
</body>
</html>
