<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox CDN Demo - Auto-Initialize</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
        }

        .feature h3 {
            margin-top: 0;
            font-size: 1.5rem;
        }

        .code-block {
            background: rgba(0,0,0,0.3);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .highlight {
            background: rgba(255,255,255,0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }

        .api-demo {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 12px;
            margin: 3rem 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .demo-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .demo-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .demo-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Simple Chatbox CDN</h1>
        <p>A lightweight, auto-initializing chatbox widget that works just like Crisp!</p>

        <div class="code-block">
            <div class="highlight">Usage - Just add one line to any website:</div>
            <br>
            &lt;script src="chatbox-cdn.js"&gt;&lt;/script&gt;
            <br><br>
            <div style="color: #a0a0a0; font-size: 0.9em;">
                // That's it! The chatbox will automatically appear 🎉
            </div>
        </div>

        <div class="api-demo">
            <h3>🔧 JavaScript API Demo</h3>
            <p>You can also control the chatbox programmatically:</p>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="ChatboxAPI.open()">Open Chat</button>
                <button class="demo-btn" onclick="ChatboxAPI.close()">Close Chat</button>
                <button class="demo-btn" onclick="ChatboxAPI.sendMessage('Hello from API!', 'user')">Send Message</button>
                <button class="demo-btn" onclick="ChatboxAPI.clear()">Clear Messages</button>
                <button class="demo-btn" onclick="alert('Open: ' + ChatboxAPI.isOpen())">Check Status</button>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🎯 Auto-Initialize</h3>
                <p>Automatically shows up when the script is loaded. Zero configuration required!</p>
            </div>

            <div class="feature">
                <h3>📱 Fully Responsive</h3>
                <p>Perfect on desktop, tablet, and mobile. Adapts to any screen size.</p>
            </div>

            <div class="feature">
                <h3>🎨 Beautiful Design</h3>
                <p>Modern gradient UI with smooth animations, typing indicators, and hover effects.</p>
            </div>

            <div class="feature">
                <h3>⚡ Super Lightweight</h3>
                <p>Pure JavaScript, zero dependencies. Complete widget in one file!</p>
            </div>

            <div class="feature">
                <h3>🤖 Smart Bot</h3>
                <p>Intelligent responses based on keywords with typing indicators for realism.</p>
            </div>

            <div class="feature">
                <h3>🔧 JavaScript API</h3>
                <p>Full programmatic control with ChatboxAPI for custom integrations.</p>
            </div>

            <div class="feature">
                <h3>⌨️ Accessibility</h3>
                <p>Keyboard navigation, ARIA labels, and screen reader support.</p>
            </div>

            <div class="feature">
                <h3>🌙 Dark Mode</h3>
                <p>Automatically adapts to system dark mode preferences.</p>
            </div>
        </div>

        <div style="margin-top: 4rem; padding: 2rem; background: rgba(0,0,0,0.2); border-radius: 12px;">
            <h3>💬 Try it now!</h3>
            <p>Look for the chat button in the bottom-right corner and start chatting!</p>
            <p style="font-size: 1rem; opacity: 0.8;">
                Try saying: "hello", "help", "thanks", or ask any question
            </p>
        </div>
    </div>

    <!-- 🚀 This single line adds the complete chatbox! -->
    <script src="chatbox-cdn.js"></script>
</body>
</html>
