<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crisp-Style Chatbox Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .hero {
            text-align: center;
            padding: 80px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
            padding: 0 20px;
        }
        
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 2.5rem;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-8px);
        }
        
        .feature h3 {
            margin-top: 0;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .feature p {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .code-section {
            background: rgba(0,0,0,0.3);
            padding: 2rem;
            border-radius: 12px;
            margin: 3rem 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .code-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffd700;
        }
        
        .code-block {
            background: rgba(0,0,0,0.5);
            padding: 1.5rem;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .highlight {
            color: #ffd700;
            font-weight: bold;
        }
        
        .chat-notice {
            position: fixed;
            bottom: 100px;
            right: 100px;
            background: rgba(255,255,255,0.95);
            color: #333;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            font-size: 0.9rem;
            max-width: 250px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        @media (max-width: 768px) {
            h1 { font-size: 2.5rem; }
            .chat-notice { display: none; }
        }
    </style>
</head>
<body>
    <div class="hero">
        <h1>🚀 Crisp-Style Chatbox</h1>
        <p class="subtitle">
            A beautiful, professional support chatbox that works exactly like Crisp.<br>
            Just add one line of code to any website!
        </p>
        
        <div class="code-section">
            <div class="code-title">✨ Usage - Add this single line to any website:</div>
            <div class="code-block">
                <span class="highlight">&lt;script src="crisp-style-chatbox.js"&gt;&lt;/script&gt;</span>
            </div>
        </div>
    </div>
    
    <div class="features">
        <div class="feature">
            <h3>💬 Professional Design</h3>
            <p>Beautiful, modern interface that matches Crisp's design language with smooth animations and professional styling.</p>
        </div>
        
        <div class="feature">
            <h3>📧 Email Collection</h3>
            <p>Automatically collects customer email and name for support follow-up, even when your team is offline.</p>
        </div>
        
        <div class="feature">
            <h3>📱 Fully Responsive</h3>
            <p>Perfect experience on desktop, tablet, and mobile devices with adaptive layouts.</p>
        </div>
        
        <div class="feature">
            <h3>⚡ Zero Dependencies</h3>
            <p>Pure JavaScript, no external libraries. Complete widget in one self-contained file.</p>
        </div>
        
        <div class="feature">
            <h3>🎯 Auto-Initialize</h3>
            <p>Just include the script and it automatically appears. No configuration or setup required.</p>
        </div>
        
        <div class="feature">
            <h3>🔧 Easy Integration</h3>
            <p>Simple API for customization and integration with your backend support system.</p>
        </div>
    </div>
    
    <div class="code-section">
        <div class="code-title">🔧 JavaScript API (Optional):</div>
        <div class="code-block">
// Open/close programmatically<br>
<span class="highlight">CrispChatbox.open();</span><br>
<span class="highlight">CrispChatbox.close();</span><br><br>

// Check status<br>
<span class="highlight">CrispChatbox.isOpen();</span><br><br>

// Reset form<br>
<span class="highlight">CrispChatbox.reset();</span>
        </div>
    </div>
    
    <div class="code-section">
        <div class="code-title">🔗 Backend Integration:</div>
        <div class="code-block">
// Replace the setTimeout in submitMessage() with:<br>
<span class="highlight">fetch('/api/support-message', {</span><br>
&nbsp;&nbsp;<span class="highlight">method: 'POST',</span><br>
&nbsp;&nbsp;<span class="highlight">headers: { 'Content-Type': 'application/json' },</span><br>
&nbsp;&nbsp;<span class="highlight">body: JSON.stringify({ email, name, message })</span><br>
<span class="highlight">});</span>
        </div>
    </div>
    
    <!-- Chat notice -->
    <div class="chat-notice">
        👆 Try the chat button!<br>
        Click the 💬 icon to test it
    </div>

    <!-- 🚀 This single line adds the complete Crisp-style chatbox! -->
    <script src="crisp-style-chatbox.js"></script>
</body>
</html>
