/**
 * Crisp-Style Chatbox CDN - Auto-initializing support chat widget
 * Usage: <script src="crisp-style-chatbox.js"></script>
 */

(function() {
    'use strict';

    console.log('🚀 Loading Crisp-Style Chatbox...');

    // Configuration
    const CONFIG = {
        welcomeMessage: 'Hi there! 👋 Send us a message and we\'ll get back to you as soon as possible.',
        placeholder: 'Type your message here...',
        title: 'Chat with us',
        companyName: 'Support Team',
        agentName: '<PERSON>',
        agentAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        submitText: 'Start Conversation',
        chatPlaceholder: 'Type a message...',
        emailPlaceholder: 'Your email address',
        namePlaceholder: 'Your name (optional)',
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedFileTypes: ['image/*', '.pdf', '.doc', '.docx', '.txt']
    };

    // Complete CSS embedded in JS
    const CSS = `
        .crisp-chatbox-trigger {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            box-shadow: 0 4px 20px rgba(30, 136, 229, 0.4) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            color: white !important;
            font-size: 24px !important;
            outline: none !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        .crisp-chatbox-trigger:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 6px 25px rgba(30, 136, 229, 0.5) !important;
        }

        .crisp-chatbox-trigger.open {
            background: #f44336 !important;
            transform: rotate(45deg) !important;
        }

        .crisp-chatbox-trigger.open:hover {
            transform: rotate(45deg) scale(1.1) !important;
            box-shadow: 0 6px 25px rgba(244, 67, 54, 0.5) !important;
        }

        .crisp-chatbox-container {
            position: fixed !important;
            bottom: 90px !important;
            right: 20px !important;
            width: 380px !important;
            height: 600px !important;
            background: white !important;
            border-radius: 16px !important;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
            z-index: 999998 !important;
            display: none;
            flex-direction: column !important;
            overflow: hidden !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            animation: slideUpFade 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .crisp-chatbox-container.open {
            display: flex !important;
        }

        @keyframes slideUpFade {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .crisp-chatbox-header {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            color: white !important;
            padding: 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            border-radius: 16px 16px 0 0 !important;
        }

        .crisp-chatbox-header-content {
            flex: 1 !important;
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
        }

        .crisp-agent-avatar {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            object-fit: cover !important;
        }

        .crisp-agent-info {
            flex: 1 !important;
        }

        .crisp-chatbox-title {
            font-weight: 600 !important;
            font-size: 16px !important;
            color: white !important;
            margin: 0 0 2px 0 !important;
        }

        .crisp-chatbox-subtitle {
            font-size: 13px !important;
            color: rgba(255, 255, 255, 0.8) !important;
            margin: 0 !important;
        }

        .crisp-agent-status {
            display: inline-flex !important;
            align-items: center !important;
            gap: 4px !important;
        }

        .crisp-status-dot {
            width: 8px !important;
            height: 8px !important;
            background: #4caf50 !important;
            border-radius: 50% !important;
            animation: pulse 2s infinite !important;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .crisp-chatbox-close {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            padding: 8px !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
            transition: background 0.2s !important;
        }

        .crisp-chatbox-close:hover {
            background: rgba(255, 255, 255, 0.2) !important;
        }

        .crisp-chatbox-content {
            flex: 1 !important;
            padding: 0 !important;
            overflow-y: auto !important;
            background: #fafafa !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .crisp-form-container {
            padding: 24px !important;
        }

        .crisp-chat-container {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
        }

        .crisp-messages-area {
            flex: 1 !important;
            padding: 20px !important;
            overflow-y: auto !important;
            max-height: 400px !important;
        }

        .crisp-message {
            margin-bottom: 16px !important;
            display: flex !important;
            animation: fadeInUp 0.3s ease !important;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .crisp-message.user {
            justify-content: flex-end !important;
        }

        .crisp-message.agent {
            justify-content: flex-start !important;
        }

        .crisp-message-content {
            max-width: 75% !important;
            padding: 12px 16px !important;
            border-radius: 18px !important;
            font-size: 14px !important;
            line-height: 1.4 !important;
            word-wrap: break-word !important;
            position: relative !important;
        }

        .crisp-message.user .crisp-message-content {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            color: white !important;
            border-radius: 18px 18px 4px 18px !important;
        }

        .crisp-message.agent .crisp-message-content {
            background: white !important;
            color: #333 !important;
            border: 1px solid #e0e0e0 !important;
            border-radius: 18px 18px 18px 4px !important;
        }

        .crisp-message-time {
            font-size: 11px !important;
            color: #999 !important;
            margin-top: 4px !important;
            text-align: center !important;
        }

        .crisp-file-message {
            background: #f5f5f5 !important;
            border: 1px solid #ddd !important;
            border-radius: 8px !important;
            padding: 12px !important;
            display: flex !important;
            align-items: center !important;
            gap: 10px !important;
            margin-top: 8px !important;
        }

        .crisp-file-icon {
            font-size: 20px !important;
        }

        .crisp-file-info {
            flex: 1 !important;
        }

        .crisp-file-name {
            font-weight: 500 !important;
            font-size: 13px !important;
            color: #333 !important;
        }

        .crisp-file-size {
            font-size: 11px !important;
            color: #666 !important;
        }

        .crisp-welcome-message {
            background: white !important;
            padding: 16px 20px !important;
            border-radius: 12px !important;
            margin-bottom: 20px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            font-size: 15px !important;
            line-height: 1.5 !important;
            color: #333 !important;
        }

        .crisp-form-group {
            margin-bottom: 16px !important;
        }

        .crisp-form-label {
            display: block !important;
            margin-bottom: 6px !important;
            font-weight: 500 !important;
            color: #555 !important;
            font-size: 14px !important;
        }

        .crisp-form-input {
            width: 100% !important;
            padding: 12px 16px !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 8px !important;
            outline: none !important;
            font-size: 15px !important;
            font-family: inherit !important;
            transition: border-color 0.2s !important;
            box-sizing: border-box !important;
        }

        .crisp-form-input:focus {
            border-color: #1e88e5 !important;
        }

        .crisp-form-textarea {
            width: 100% !important;
            padding: 12px 16px !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 8px !important;
            outline: none !important;
            font-size: 15px !important;
            font-family: inherit !important;
            transition: border-color 0.2s !important;
            box-sizing: border-box !important;
            resize: vertical !important;
            min-height: 100px !important;
        }

        .crisp-form-textarea:focus {
            border-color: #1e88e5 !important;
        }

        .crisp-submit-btn {
            width: 100% !important;
            padding: 14px 20px !important;
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.2s !important;
            outline: none !important;
            margin-top: 8px !important;
        }

        .crisp-submit-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3) !important;
        }

        .crisp-submit-btn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

        .crisp-success-message {
            text-align: center !important;
            padding: 40px 20px !important;
            color: #4caf50 !important;
        }

        .crisp-success-icon {
            font-size: 48px !important;
            margin-bottom: 16px !important;
        }

        .crisp-success-text {
            font-size: 16px !important;
            font-weight: 500 !important;
            margin-bottom: 8px !important;
        }

        .crisp-success-subtext {
            font-size: 14px !important;
            color: #666 !important;
        }

        .crisp-chat-input-area {
            padding: 16px 20px !important;
            border-top: 1px solid #e0e0e0 !important;
            background: white !important;
        }

        .crisp-input-container {
            display: flex !important;
            align-items: flex-end !important;
            gap: 8px !important;
            background: #f8f9fa !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 24px !important;
            padding: 8px 12px !important;
            transition: border-color 0.2s !important;
        }

        .crisp-input-container:focus-within {
            border-color: #1e88e5 !important;
        }

        .crisp-chat-input {
            flex: 1 !important;
            border: none !important;
            outline: none !important;
            background: transparent !important;
            font-size: 14px !important;
            font-family: inherit !important;
            resize: none !important;
            max-height: 100px !important;
            min-height: 20px !important;
            padding: 6px 0 !important;
        }

        .crisp-input-actions {
            display: flex !important;
            align-items: center !important;
            gap: 4px !important;
        }

        .crisp-file-upload-btn {
            background: none !important;
            border: none !important;
            color: #666 !important;
            cursor: pointer !important;
            padding: 6px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.2s !important;
            outline: none !important;
        }

        .crisp-file-upload-btn:hover {
            background: #e0e0e0 !important;
            color: #1e88e5 !important;
        }

        .crisp-send-btn {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            border: none !important;
            color: white !important;
            cursor: pointer !important;
            padding: 8px !important;
            border-radius: 50% !important;
            font-size: 16px !important;
            width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s !important;
            outline: none !important;
        }

        .crisp-send-btn:hover {
            transform: scale(1.05) !important;
        }

        .crisp-send-btn:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

        .crisp-file-input {
            display: none !important;
        }

        .crisp-typing-indicator {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            padding: 12px 16px !important;
            color: #666 !important;
            font-size: 13px !important;
            font-style: italic !important;
        }

        .crisp-typing-dots {
            display: flex !important;
            gap: 3px !important;
        }

        .crisp-typing-dot {
            width: 4px !important;
            height: 4px !important;
            background: #999 !important;
            border-radius: 50% !important;
            animation: typingBounce 1.4s infinite ease-in-out !important;
        }

        .crisp-typing-dot:nth-child(1) { animation-delay: -0.32s !important; }
        .crisp-typing-dot:nth-child(2) { animation-delay: -0.16s !important; }

        @keyframes typingBounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 480px) {
            .crisp-chatbox-container {
                width: calc(100vw - 40px) !important;
                height: calc(100vh - 40px) !important;
                bottom: 20px !important;
                right: 20px !important;
                left: 20px !important;
                top: 20px !important;
                border-radius: 0 !important;
            }
            
            .crisp-chatbox-header {
                border-radius: 0 !important;
            }
        }
    `;

    // Global variables
    let isOpen = false;
    let isSubmitted = false;
    let isChatMode = false;
    let messages = [];
    let userInfo = { name: '', email: '' };

    // Inject CSS
    function injectCSS() {
        console.log('💄 Injecting CSS');
        const style = document.createElement('style');
        style.textContent = CSS;
        document.head.appendChild(style);
    }

    // Create chatbox elements
    function createChatbox() {
        console.log('🏗️ Creating chatbox elements');

        // Create trigger button
        const trigger = document.createElement('button');
        trigger.className = 'crisp-chatbox-trigger';
        trigger.innerHTML = '💬';
        trigger.onclick = toggleChatbox;
        trigger.setAttribute('aria-label', 'Open chat');

        // Create container
        const container = document.createElement('div');
        container.className = 'crisp-chatbox-container';
        container.id = 'crisp-chatbox-container';
        container.innerHTML = `
            <div class="crisp-chatbox-header">
                <div class="crisp-chatbox-header-content">
                    <img src="${CONFIG.agentAvatar}" alt="Agent" class="crisp-agent-avatar" onerror="this.style.display='none'">
                    <div class="crisp-agent-info">
                        <div class="crisp-chatbox-title">${CONFIG.title}</div>
                        <div class="crisp-chatbox-subtitle">
                            <div class="crisp-agent-status">
                                <span class="crisp-status-dot"></span>
                                ${CONFIG.agentName} - Online
                            </div>
                        </div>
                    </div>
                </div>
                <button class="crisp-chatbox-close" onclick="closeChatbox()" aria-label="Close chat">×</button>
            </div>
            <div class="crisp-chatbox-content" id="crisp-chatbox-content">
                <!-- Initial Form -->
                <div class="crisp-form-container" id="crisp-form-container">
                    <div class="crisp-welcome-message">
                        ${CONFIG.welcomeMessage}
                    </div>
                    <form id="crisp-support-form" onsubmit="startChat(event)">
                        <div class="crisp-form-group">
                            <label class="crisp-form-label" for="crisp-email">Email *</label>
                            <input type="email" id="crisp-email" class="crisp-form-input" placeholder="${CONFIG.emailPlaceholder}" required>
                        </div>
                        <div class="crisp-form-group">
                            <label class="crisp-form-label" for="crisp-name">Name</label>
                            <input type="text" id="crisp-name" class="crisp-form-input" placeholder="${CONFIG.namePlaceholder}">
                        </div>
                        <div class="crisp-form-group">
                            <label class="crisp-form-label" for="crisp-message">Message *</label>
                            <textarea id="crisp-message" class="crisp-form-textarea" placeholder="${CONFIG.placeholder}" required></textarea>
                        </div>
                        <button type="submit" class="crisp-submit-btn" id="crisp-submit-btn">
                            ${CONFIG.submitText}
                        </button>
                    </form>
                </div>

                <!-- Chat Interface -->
                <div class="crisp-chat-container" id="crisp-chat-container" style="display: none;">
                    <div class="crisp-messages-area" id="crisp-messages-area">
                        <!-- Messages will be added here -->
                    </div>
                    <div class="crisp-typing-indicator" id="crisp-typing-indicator" style="display: none;">
                        <div class="crisp-typing-dots">
                            <div class="crisp-typing-dot"></div>
                            <div class="crisp-typing-dot"></div>
                            <div class="crisp-typing-dot"></div>
                        </div>
                        ${CONFIG.agentName} is typing...
                    </div>
                    <div class="crisp-chat-input-area">
                        <div class="crisp-input-container">
                            <button class="crisp-file-upload-btn" onclick="triggerFileUpload()" title="Attach file">
                                📎
                            </button>
                            <textarea class="crisp-chat-input" id="crisp-chat-input" placeholder="${CONFIG.chatPlaceholder}" rows="1"></textarea>
                            <button class="crisp-send-btn" onclick="sendChatMessage()" title="Send message">
                                ➤
                            </button>
                        </div>
                        <input type="file" class="crisp-file-input" id="crisp-file-input" multiple accept="${CONFIG.allowedFileTypes.join(',')}" onchange="handleFileUpload(event)">
                    </div>
                </div>
            </div>
        `;

        // Append to body
        document.body.appendChild(trigger);
        document.body.appendChild(container);

        console.log('✅ Chatbox created successfully');
    }

    // Toggle chatbox
    function toggleChatbox() {
        console.log('🔄 Toggling chatbox');
        const container = document.getElementById('crisp-chatbox-container');
        const trigger = document.querySelector('.crisp-chatbox-trigger');

        if (container && trigger) {
            isOpen = !isOpen;
            container.classList.toggle('open', isOpen);
            trigger.classList.toggle('open', isOpen);
            trigger.innerHTML = isOpen ? '×' : '💬';

            if (isOpen) {
                setTimeout(() => {
                    if (isChatMode) {
                        const chatInput = document.getElementById('crisp-chat-input');
                        if (chatInput) chatInput.focus();
                    } else {
                        const emailInput = document.getElementById('crisp-email');
                        if (emailInput) emailInput.focus();
                    }
                }, 100);
            }
        }
    }

    // Close chatbox
    function closeChatbox() {
        console.log('❌ Closing chatbox');
        const container = document.getElementById('crisp-chatbox-container');
        const trigger = document.querySelector('.crisp-chatbox-trigger');

        if (container && trigger) {
            isOpen = false;
            container.classList.remove('open');
            trigger.classList.remove('open');
            trigger.innerHTML = '💬';
        }
    }

    // Start chat conversation
    function startChat(event) {
        event.preventDefault();

        const submitBtn = document.getElementById('crisp-submit-btn');
        const email = document.getElementById('crisp-email').value;
        const name = document.getElementById('crisp-name').value;
        const message = document.getElementById('crisp-message').value;

        if (!email || !message) return;

        // Store user info
        userInfo = { name: name || 'Guest', email };

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Starting chat...';

        // Switch to chat mode
        setTimeout(() => {
            switchToChatMode(message);
        }, 500);
    }

    // Switch from form to chat interface
    function switchToChatMode(initialMessage) {
        const formContainer = document.getElementById('crisp-form-container');
        const chatContainer = document.getElementById('crisp-chat-container');

        formContainer.style.display = 'none';
        chatContainer.style.display = 'flex';

        isChatMode = true;

        // Add initial user message
        addMessage(initialMessage, 'user');

        // Show typing indicator and respond
        showTypingIndicator();
        setTimeout(() => {
            hideTypingIndicator();
            addMessage(`Hi ${userInfo.name}! Thanks for reaching out. I'm here to help you. What can I assist you with today?`, 'agent');
        }, 1500);

        // Setup chat input
        setupChatInput();

        console.log('💬 Chat started for:', userInfo);
    }

    // Setup chat input functionality
    function setupChatInput() {
        const chatInput = document.getElementById('crisp-chat-input');

        // Auto-resize textarea
        chatInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Send on Enter (but allow Shift+Enter for new lines)
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });

        // Focus on input
        setTimeout(() => chatInput.focus(), 100);
    }

    // Send chat message
    function sendChatMessage() {
        const chatInput = document.getElementById('crisp-chat-input');
        const message = chatInput.value.trim();

        if (!message) return;

        // Add user message
        addMessage(message, 'user');
        chatInput.value = '';
        chatInput.style.height = 'auto';

        // Simulate agent response
        showTypingIndicator();
        setTimeout(() => {
            hideTypingIndicator();
            const response = generateAgentResponse(message);
            addMessage(response, 'agent');
        }, Math.random() * 2000 + 1000); // 1-3 seconds
    }

    // Add message to chat
    function addMessage(text, sender, file = null) {
        const messagesArea = document.getElementById('crisp-messages-area');
        const messageDiv = document.createElement('div');
        messageDiv.className = `crisp-message ${sender}`;

        const now = new Date();
        const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        let fileHtml = '';
        if (file) {
            const fileIcon = getFileIcon(file.type);
            const fileSize = formatFileSize(file.size);
            fileHtml = `
                <div class="crisp-file-message">
                    <span class="crisp-file-icon">${fileIcon}</span>
                    <div class="crisp-file-info">
                        <div class="crisp-file-name">${file.name}</div>
                        <div class="crisp-file-size">${fileSize}</div>
                    </div>
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="crisp-message-content">
                ${text}
                ${fileHtml}
            </div>
            <div class="crisp-message-time">${timeStr}</div>
        `;

        messagesArea.appendChild(messageDiv);
        messagesArea.scrollTop = messagesArea.scrollHeight;

        // Store message
        messages.push({
            text,
            sender,
            file,
            timestamp: now.toISOString(),
            user: userInfo
        });

        console.log('💬 Message added:', { text, sender, file: file?.name });
    }

    // Show typing indicator
    function showTypingIndicator() {
        const indicator = document.getElementById('crisp-typing-indicator');
        if (indicator) {
            indicator.style.display = 'flex';
            const messagesArea = document.getElementById('crisp-messages-area');
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        const indicator = document.getElementById('crisp-typing-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    // Generate agent response
    function generateAgentResponse(userMessage) {
        const responses = {
            hello: "Hello! Great to meet you. How can I help you today?",
            hi: "Hi there! What can I assist you with?",
            help: "I'm here to help! What specific issue are you facing?",
            thanks: "You're very welcome! Is there anything else I can help you with?",
            thank: "Happy to help! Let me know if you need anything else.",
            bye: "Thank you for chatting with us! Have a wonderful day! 👋",
            goodbye: "Take care! Feel free to reach out anytime you need help.",
            price: "I'd be happy to help you with pricing information. Let me get those details for you.",
            problem: "I'm sorry to hear you're having an issue. Can you tell me more about what's happening?",
            error: "Let's get that error resolved for you. Can you describe what you're seeing?",
            bug: "Thanks for reporting this! Can you provide more details about the bug?",
            feature: "That's a great suggestion! I'll make sure our team knows about this feature request.",
            default: "I understand. Let me help you with that. Can you provide a bit more detail?"
        };

        const lower = userMessage.toLowerCase();

        // Check for keywords
        for (const [key, value] of Object.entries(responses)) {
            if (key !== 'default' && lower.includes(key)) {
                return value;
            }
        }

        // Add some personality based on message length
        if (userMessage.length > 100) {
            return "Thanks for the detailed explanation! " + responses.default;
        }

        return responses.default;
    }

    // File upload functions
    function triggerFileUpload() {
        const fileInput = document.getElementById('crisp-file-input');
        fileInput.click();
    }

    function handleFileUpload(event) {
        const files = Array.from(event.target.files);

        files.forEach(file => {
            if (validateFile(file)) {
                addMessage(`Uploaded: ${file.name}`, 'user', file);

                // Simulate agent response to file
                showTypingIndicator();
                setTimeout(() => {
                    hideTypingIndicator();
                    addMessage("Thanks for sharing that file! I've received it and will review it shortly.", 'agent');
                }, 1000);
            }
        });

        // Clear input
        event.target.value = '';
    }

    function validateFile(file) {
        // Check file size
        if (file.size > CONFIG.maxFileSize) {
            alert(`File "${file.name}" is too large. Maximum size is ${formatFileSize(CONFIG.maxFileSize)}.`);
            return false;
        }

        // Check file type
        const isValidType = CONFIG.allowedFileTypes.some(type => {
            if (type.includes('*')) {
                return file.type.startsWith(type.replace('*', ''));
            }
            return file.name.toLowerCase().endsWith(type);
        });

        if (!isValidType) {
            alert(`File type "${file.type}" is not allowed.`);
            return false;
        }

        return true;
    }

    function getFileIcon(fileType) {
        if (fileType.startsWith('image/')) return '🖼️';
        if (fileType.includes('pdf')) return '📄';
        if (fileType.includes('word') || fileType.includes('doc')) return '📝';
        if (fileType.includes('text')) return '📄';
        return '📎';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Make functions global
    window.toggleChatbox = toggleChatbox;
    window.closeChatbox = closeChatbox;
    window.startChat = startChat;
    window.sendChatMessage = sendChatMessage;
    window.triggerFileUpload = triggerFileUpload;
    window.handleFileUpload = handleFileUpload;

    // Public API
    window.CrispChatbox = {
        // Basic controls
        open: toggleChatbox,
        close: closeChatbox,
        isOpen: () => isOpen,

        // Chat functions
        sendMessage: (message) => {
            if (isChatMode) {
                addMessage(message, 'agent');
            }
        },

        // Get data
        getMessages: () => messages,
        getUserInfo: () => userInfo,
        isChatMode: () => isChatMode,

        // Reset
        reset: () => {
            isSubmitted = false;
            isChatMode = false;
            messages = [];
            userInfo = { name: '', email: '' };
            const container = document.getElementById('crisp-chatbox-container');
            if (container) {
                container.remove();
                createChatbox();
            }
        },

        // Configuration
        config: CONFIG
    };

    // Initialize
    function init() {
        console.log('🎯 Initializing Crisp-style chatbox');

        injectCSS();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createChatbox);
        } else {
            createChatbox();
        }
    }

    // Start initialization
    init();

})();
