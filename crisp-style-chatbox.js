/**
 * Crisp-Style Chatbox CDN - Auto-initializing support chat widget
 * Usage: <script src="crisp-style-chatbox.js"></script>
 */

(function() {
    'use strict';

    console.log('🚀 Loading Crisp-Style Chatbox...');

    // Configuration
    const CONFIG = {
        welcomeMessage: 'Hi there! 👋 Send us a message and we\'ll get back to you as soon as possible.',
        placeholder: 'Type your message here...',
        title: 'Send us a message',
        companyName: 'Support Team',
        submitText: 'Send Message',
        thankYouMessage: 'Thanks for your message! We\'ll get back to you soon.',
        emailPlaceholder: 'Your email address',
        namePlaceholder: 'Your name (optional)'
    };

    // Complete CSS embedded in JS
    const CSS = `
        .crisp-chatbox-trigger {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            border: none !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            box-shadow: 0 4px 20px rgba(30, 136, 229, 0.4) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            color: white !important;
            font-size: 24px !important;
            outline: none !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        .crisp-chatbox-trigger:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 6px 25px rgba(30, 136, 229, 0.5) !important;
        }

        .crisp-chatbox-trigger.open {
            background: #f44336 !important;
            transform: rotate(45deg) !important;
        }

        .crisp-chatbox-trigger.open:hover {
            transform: rotate(45deg) scale(1.1) !important;
            box-shadow: 0 6px 25px rgba(244, 67, 54, 0.5) !important;
        }

        .crisp-chatbox-container {
            position: fixed !important;
            bottom: 90px !important;
            right: 20px !important;
            width: 380px !important;
            height: 600px !important;
            background: white !important;
            border-radius: 16px !important;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
            z-index: 999998 !important;
            display: none !important;
            flex-direction: column !important;
            overflow: hidden !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            animation: slideUpFade 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        @keyframes slideUpFade {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .crisp-chatbox-header {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            color: white !important;
            padding: 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            border-radius: 16px 16px 0 0 !important;
        }

        .crisp-chatbox-header-content {
            flex: 1 !important;
        }

        .crisp-chatbox-title {
            font-weight: 600 !important;
            font-size: 18px !important;
            color: white !important;
            margin: 0 0 4px 0 !important;
        }

        .crisp-chatbox-subtitle {
            font-size: 14px !important;
            color: rgba(255, 255, 255, 0.9) !important;
            margin: 0 !important;
        }

        .crisp-chatbox-close {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            padding: 8px !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            outline: none !important;
            transition: background 0.2s !important;
        }

        .crisp-chatbox-close:hover {
            background: rgba(255, 255, 255, 0.2) !important;
        }

        .crisp-chatbox-content {
            flex: 1 !important;
            padding: 24px !important;
            overflow-y: auto !important;
            background: #fafafa !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .crisp-welcome-message {
            background: white !important;
            padding: 16px 20px !important;
            border-radius: 12px !important;
            margin-bottom: 20px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            font-size: 15px !important;
            line-height: 1.5 !important;
            color: #333 !important;
        }

        .crisp-form-group {
            margin-bottom: 16px !important;
        }

        .crisp-form-label {
            display: block !important;
            margin-bottom: 6px !important;
            font-weight: 500 !important;
            color: #555 !important;
            font-size: 14px !important;
        }

        .crisp-form-input {
            width: 100% !important;
            padding: 12px 16px !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 8px !important;
            outline: none !important;
            font-size: 15px !important;
            font-family: inherit !important;
            transition: border-color 0.2s !important;
            box-sizing: border-box !important;
        }

        .crisp-form-input:focus {
            border-color: #1e88e5 !important;
        }

        .crisp-form-textarea {
            width: 100% !important;
            padding: 12px 16px !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 8px !important;
            outline: none !important;
            font-size: 15px !important;
            font-family: inherit !important;
            transition: border-color 0.2s !important;
            box-sizing: border-box !important;
            resize: vertical !important;
            min-height: 100px !important;
        }

        .crisp-form-textarea:focus {
            border-color: #1e88e5 !important;
        }

        .crisp-submit-btn {
            width: 100% !important;
            padding: 14px 20px !important;
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.2s !important;
            outline: none !important;
            margin-top: 8px !important;
        }

        .crisp-submit-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3) !important;
        }

        .crisp-submit-btn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

        .crisp-success-message {
            text-align: center !important;
            padding: 40px 20px !important;
            color: #4caf50 !important;
        }

        .crisp-success-icon {
            font-size: 48px !important;
            margin-bottom: 16px !important;
        }

        .crisp-success-text {
            font-size: 16px !important;
            font-weight: 500 !important;
            margin-bottom: 8px !important;
        }

        .crisp-success-subtext {
            font-size: 14px !important;
            color: #666 !important;
        }

        @media (max-width: 480px) {
            .crisp-chatbox-container {
                width: calc(100vw - 40px) !important;
                height: calc(100vh - 40px) !important;
                bottom: 20px !important;
                right: 20px !important;
                left: 20px !important;
                top: 20px !important;
                border-radius: 0 !important;
            }
            
            .crisp-chatbox-header {
                border-radius: 0 !important;
            }
        }
    `;

    // Global variables
    let isOpen = false;
    let isSubmitted = false;

    // Inject CSS
    function injectCSS() {
        console.log('💄 Injecting CSS');
        const style = document.createElement('style');
        style.textContent = CSS;
        document.head.appendChild(style);
    }

    // Create chatbox elements
    function createChatbox() {
        console.log('🏗️ Creating chatbox elements');

        // Create trigger button
        const trigger = document.createElement('button');
        trigger.className = 'crisp-chatbox-trigger';
        trigger.innerHTML = '💬';
        trigger.onclick = toggleChatbox;
        trigger.setAttribute('aria-label', 'Open chat');

        // Create container
        const container = document.createElement('div');
        container.className = 'crisp-chatbox-container';
        container.id = 'crisp-chatbox-container';
        container.innerHTML = `
            <div class="crisp-chatbox-header">
                <div class="crisp-chatbox-header-content">
                    <div class="crisp-chatbox-title">${CONFIG.title}</div>
                    <div class="crisp-chatbox-subtitle">We typically reply in a few minutes</div>
                </div>
                <button class="crisp-chatbox-close" onclick="closeChatbox()" aria-label="Close chat">×</button>
            </div>
            <div class="crisp-chatbox-content" id="crisp-chatbox-content">
                <div class="crisp-welcome-message">
                    ${CONFIG.welcomeMessage}
                </div>
                <form id="crisp-support-form" onsubmit="submitMessage(event)">
                    <div class="crisp-form-group">
                        <label class="crisp-form-label" for="crisp-email">Email *</label>
                        <input type="email" id="crisp-email" class="crisp-form-input" placeholder="${CONFIG.emailPlaceholder}" required>
                    </div>
                    <div class="crisp-form-group">
                        <label class="crisp-form-label" for="crisp-name">Name</label>
                        <input type="text" id="crisp-name" class="crisp-form-input" placeholder="${CONFIG.namePlaceholder}">
                    </div>
                    <div class="crisp-form-group">
                        <label class="crisp-form-label" for="crisp-message">Message *</label>
                        <textarea id="crisp-message" class="crisp-form-textarea" placeholder="${CONFIG.placeholder}" required></textarea>
                    </div>
                    <button type="submit" class="crisp-submit-btn" id="crisp-submit-btn">
                        ${CONFIG.submitText}
                    </button>
                </form>
            </div>
        `;

        // Append to body
        document.body.appendChild(trigger);
        document.body.appendChild(container);

        console.log('✅ Chatbox created successfully');
    }

    // Toggle chatbox
    function toggleChatbox() {
        console.log('🔄 Toggling chatbox');
        const container = document.getElementById('crisp-chatbox-container');
        const trigger = document.querySelector('.crisp-chatbox-trigger');

        if (container && trigger) {
            isOpen = !isOpen;
            container.style.display = isOpen ? 'flex' : 'none';
            trigger.classList.toggle('open', isOpen);
            trigger.innerHTML = isOpen ? '×' : '💬';

            if (isOpen && !isSubmitted) {
                setTimeout(() => {
                    const emailInput = document.getElementById('crisp-email');
                    if (emailInput) emailInput.focus();
                }, 100);
            }
        }
    }

    // Close chatbox
    function closeChatbox() {
        console.log('❌ Closing chatbox');
        const container = document.getElementById('crisp-chatbox-container');
        const trigger = document.querySelector('.crisp-chatbox-trigger');

        if (container && trigger) {
            isOpen = false;
            container.style.display = 'none';
            trigger.classList.remove('open');
            trigger.innerHTML = '💬';
        }
    }

    // Submit support message
    function submitMessage(event) {
        event.preventDefault();

        const submitBtn = document.getElementById('crisp-submit-btn');
        const email = document.getElementById('crisp-email').value;
        const name = document.getElementById('crisp-name').value;
        const message = document.getElementById('crisp-message').value;

        if (!email || !message) return;

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Sending...';

        // Simulate sending message (replace with your actual API call)
        console.log('📧 Sending support message:', { email, name, message });

        // You can replace this with your actual API call
        setTimeout(() => {
            showSuccessMessage();
            isSubmitted = true;

            // Optional: Send to your backend
            // fetch('/api/support-message', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ email, name, message })
            // });

        }, 1000);
    }

    // Show success message
    function showSuccessMessage() {
        const content = document.getElementById('crisp-chatbox-content');
        content.innerHTML = `
            <div class="crisp-success-message">
                <div class="crisp-success-icon">✅</div>
                <div class="crisp-success-text">${CONFIG.thankYouMessage}</div>
                <div class="crisp-success-subtext">We'll respond to your email as soon as possible.</div>
            </div>
        `;
    }

    // Make functions global
    window.toggleChatbox = toggleChatbox;
    window.closeChatbox = closeChatbox;
    window.submitMessage = submitMessage;

    // Public API
    window.CrispChatbox = {
        open: toggleChatbox,
        close: closeChatbox,
        isOpen: () => isOpen,
        reset: () => {
            isSubmitted = false;
            const container = document.getElementById('crisp-chatbox-container');
            if (container) {
                container.remove();
                createChatbox();
            }
        }
    };

    // Initialize
    function init() {
        console.log('🎯 Initializing Crisp-style chatbox');

        injectCSS();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createChatbox);
        } else {
            createChatbox();
        }
    }

    // Start initialization
    init();

})();
